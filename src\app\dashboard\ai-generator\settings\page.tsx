'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/providers/AuthProvider'
import { useRouter } from 'next/navigation'
import {
  CogIcon,
  CheckIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'
import {
  DashboardCard,
  DashboardCardContent,
  DashboardCardDescription,
  DashboardCardHeader,
  DashboardCardTitle
} from '@/components/dashboard/DashboardCard'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { getAIProviderStatus } from '@/lib/ai'
import {
  getOrCreateAISettings,
  getUserAISettings,
  saveAISettings,
  validateAISettings,
  getProviderUsageStats
} from '@/lib/ai/settings'
import { AISettings, AIProvider, AI_MODELS, DEFAULT_AI_SETTINGS } from '@/types/ai'

export default function AISettingsPage() {
  const { user, loading: authLoading } = useAuth()
  const router = useRouter()
  const [settings, setSettings] = useState<AISettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [errors, setErrors] = useState<string[]>([])
  const [successMessage, setSuccessMessage] = useState('')
  const [providerStatus, setProviderStatus] = useState({
    openai: false,
    gemini: false,
    openrouter: false
  })

  // Analytics state (simplified for settings page)
  const [providerStats, setProviderStats] = useState<any[]>([])
  const [analyticsLoading, setAnalyticsLoading] = useState(true)

  useEffect(() => {
    if (!authLoading && user) {
      console.log('User authenticated in settings:', user.uid)
      loadSettings()
      loadAnalytics()
    } else if (!authLoading && !user) {
      console.log('No user authenticated')
      // Don't redirect automatically, let the middleware handle it
    }
  }, [user, authLoading])



  const loadSettings = async () => {
    if (!user) return

    try {
      // Try to load existing settings first
      const existingSettings = await getUserAISettings(user.uid)

      if (existingSettings) {
        setSettings(existingSettings)
      } else {
        // No existing settings, use defaults but don't save yet
        const defaultSettings = {
          ...DEFAULT_AI_SETTINGS,
          user_id: user.uid,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
        setSettings(defaultSettings)
        setErrors(['Using default settings. You can customize them below.'])
      }

      setProviderStatus(getAIProviderStatus())
    } catch (error) {
      console.error('Error loading settings:', error)
      // If loading fails, create default settings
      const defaultSettings = {
        ...DEFAULT_AI_SETTINGS,
        user_id: user.uid,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      setSettings(defaultSettings)
      setProviderStatus(getAIProviderStatus())
      setErrors(['Failed to load AI settings. Using defaults. You can customize them below.'])
    } finally {
      setLoading(false)
    }
  }

  const loadAnalytics = async () => {
    if (!user) return

    setAnalyticsLoading(true)
    try {
      // Load provider usage stats for the last 30 days (for AI Providers section)
      const stats = await getProviderUsageStats(user.uid)
      setProviderStats(stats)
    } catch (error) {
      console.error('Error loading analytics:', error)
    } finally {
      setAnalyticsLoading(false)
    }
  }

  const handleProviderChange = (provider: AIProvider) => {
    if (!settings) return

    // Update provider and reset models to defaults for that provider
    const defaultModels = {
      research: AI_MODELS[provider][0]?.id || '',
      outline: AI_MODELS[provider][0]?.id || '',
      content: AI_MODELS[provider][0]?.id || '',
      metadata: AI_MODELS[provider][0]?.id || ''
    }

    setSettings({
      ...settings,
      provider,
      models: defaultModels
    })
  }

  const handleModelChange = (taskType: keyof AISettings['models'], modelId: string) => {
    if (!settings) return

    setSettings({
      ...settings,
      models: {
        ...settings.models,
        [taskType]: modelId
      }
    })
  }

  const handlePreferenceChange = (key: keyof AISettings['preferences'], value: any) => {
    if (!settings) return

    setSettings({
      ...settings,
      preferences: {
        ...settings.preferences,
        [key]: value
      }
    })
  }

  const handleSave = async () => {
    if (!settings || !user) {
      setErrors(['User not authenticated. Please sign in again.'])
      return
    }

    setSaving(true)
    setErrors([])
    setSuccessMessage('')

    try {
      // Wait a bit to ensure Firebase Auth is fully initialized
      await new Promise(resolve => setTimeout(resolve, 100))

      // Debug: Check user authentication
      console.log('User authenticated:', user.uid)
      console.log('User email:', user.email)
      console.log('Settings to save:', settings)

      // Validate settings
      const validationErrors = validateAISettings(settings)
      if (validationErrors.length > 0) {
        setErrors(validationErrors)
        return
      }

      // Prepare settings for saving (exclude fields that should be auto-generated)
      const { id, user_id, created_at, updated_at, ...settingsToSave } = settings

      // Debug: Check what we're saving
      console.log('Prepared settings:', settingsToSave)
      console.log('User ID:', user.uid)

      // Save settings directly to client-side Firebase
      const settingsId = await saveAISettings(user.uid, settingsToSave)
      console.log('Settings saved with ID:', settingsId)

      setSuccessMessage('AI settings saved successfully!')

      // Clear the error message since save was successful
      setErrors([])

      // Redirect back to main AI generator page after a delay
      setTimeout(() => {
        router.push('/dashboard/ai-generator')
      }, 2000)
    } catch (error) {
      console.error('Error saving settings:', error)

      // More detailed error handling
      let errorMessage = 'Unknown error'
      if (error instanceof Error) {
        errorMessage = error.message

        // Check for specific Firebase errors
        if (error.message.includes('permission-denied') || error.message.includes('Missing or insufficient permissions')) {
          errorMessage = 'Permission denied. Please try signing out and signing back in.'
        } else if (error.message.includes('unauthenticated')) {
          errorMessage = 'User not authenticated. Please sign in again.'
        }
      }

      setErrors([`Failed to save settings: ${errorMessage}`])
      setSuccessMessage('')
    } finally {
      setSaving(false)
    }
  }

  const resetToDefaults = () => {
    if (!user) return

    setSettings({
      ...DEFAULT_AI_SETTINGS,
      id: settings?.id,
      user_id: user.uid,
      created_at: settings?.created_at || new Date().toISOString(),
      updated_at: new Date().toISOString()
    })
  }

  if (authLoading || loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading AI settings...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null // Let the dashboard layout handle authentication
  }

  if (!settings) {
    return (
      <div className="text-center py-12">
        <ExclamationTriangleIcon className="w-12 h-12 text-red-500 mx-auto mb-4" />
        <h2 className="text-xl font-semibold mb-2">Failed to Load Settings</h2>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          Unable to load AI settings. Please try again.
        </p>
        <Button onClick={loadSettings}>Retry</Button>
      </div>
    )
  }

  const availableProviders = Object.entries(providerStatus)
    .filter(([_, available]) => available)
    .map(([provider, _]) => provider as AIProvider)

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight dashboard-text flex items-center gap-3">
            <CogIcon className="w-8 h-8 text-blue-600" />
            AI Settings
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2 text-lg">
            Configure your AI providers and generation preferences
          </p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" onClick={resetToDefaults} size="lg">
            Reset to Defaults
          </Button>
          <Button onClick={handleSave} disabled={saving} size="lg" className="bg-blue-600 hover:bg-blue-700 text-white">
            {saving ? 'Saving...' : 'Save Settings'}
          </Button>
        </div>
      </div>

      {/* Messages */}
      {errors.length > 0 && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-start">
            <ExclamationTriangleIcon className="w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
            <div>
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Please fix the following errors:
              </h3>
              <ul className="mt-2 text-sm text-red-700 dark:text-red-300 list-disc list-inside">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      {successMessage && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-center">
            <CheckIcon className="w-5 h-5 text-green-500 mr-3" />
            <p className="text-sm text-green-800 dark:text-green-200">{successMessage}</p>
          </div>
        </div>
      )}

      {/* AI Providers with Usage Stats */}
      <DashboardCard className="border-2 border-blue-100 dark:border-blue-900">
        <DashboardCardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-t-lg">
          <DashboardCardTitle className="text-xl">AI Providers</DashboardCardTitle>
          <DashboardCardDescription>
            Available AI providers for content generation
          </DashboardCardDescription>
        </DashboardCardHeader>
        <DashboardCardContent className="p-6">
          <div className="space-y-4">
            {(['openai', 'gemini', 'openrouter'] as const).map((provider) => {
              const isAvailable = providerStatus[provider]
              const isSelected = settings.provider === provider
              const stats = providerStats.find(s => s.provider === provider)

              const providerInfo = {
                openai: { name: 'OpenAI', icon: '●' },
                gemini: { name: 'Gemini', icon: '●' },
                openrouter: { name: 'OpenRouter', icon: '●' }
              }

              return (
                <div
                  key={provider}
                  className={`flex items-center justify-between p-4 rounded-lg transition-all duration-200 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800/50 ${
                    isSelected ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                  }`}
                  onClick={() => isAvailable && handleProviderChange(provider)}
                >
                  <div className="flex items-center gap-3">
                    <span className={`text-lg ${isAvailable ? 'text-green-500' : 'text-gray-400'}`}>
                      {providerInfo[provider].icon}
                    </span>
                    <span className="font-medium text-lg">
                      {providerInfo[provider].name}
                    </span>
                  </div>

                  <div className="flex items-center gap-8">
                    {/* Token Usage */}
                    <div className="text-right">
                      {analyticsLoading ? (
                        <div className="h-4 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                      ) : stats ? (
                        <span className="text-sm font-medium">
                          {stats.totalTokens.toLocaleString()} tokens
                        </span>
                      ) : (
                        <span className="text-sm text-gray-400">No usage</span>
                      )}
                    </div>

                    {/* Generation Cost */}
                    <div className="text-right">
                      {analyticsLoading ? (
                        <div className="h-4 w-12 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                      ) : stats ? (
                        <span className="text-sm font-medium text-green-600 dark:text-green-400">
                          ${stats.totalCost.toFixed(3)}
                        </span>
                      ) : (
                        <span className="text-sm text-gray-400">$0.00</span>
                      )}
                    </div>

                    {/* Status Badge */}
                    <Badge
                      className={
                        isAvailable
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                          : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                      }
                    >
                      {isAvailable ? 'Available' : 'Not Configured'}
                    </Badge>
                  </div>
                </div>
              )
            })}
          </div>

          {availableProviders.length === 0 && (
            <div className="mt-6 p-6 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-xl">
              <div className="flex items-start">
                <InformationCircleIcon className="w-6 h-6 text-yellow-500 mt-0.5 mr-4 flex-shrink-0" />
                <div>
                  <h3 className="text-lg font-medium text-yellow-800 dark:text-yellow-200">
                    No AI Providers Available
                  </h3>
                  <p className="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                    Please configure at least one AI provider in your environment variables to use the AI blog generator.
                  </p>
                </div>
              </div>
            </div>
          )}
        </DashboardCardContent>
      </DashboardCard>



      {/* Model Selection */}
      {settings.provider && providerStatus[settings.provider] && (
        <DashboardCard>
          <DashboardCardHeader>
            <DashboardCardTitle className="text-xl">Model Selection</DashboardCardTitle>
            <DashboardCardDescription>
              Choose specific models for different tasks to optimize cost and quality
            </DashboardCardDescription>
          </DashboardCardHeader>
          <DashboardCardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {Object.entries(settings.models).map(([taskType, selectedModel]) => {
                const taskIcons = {
                  research: '🔍',
                  outline: '📋',
                  content: '✍️',
                  metadata: '🏷️'
                }

                const taskDescriptions = {
                  research: 'Keyword research and topic analysis',
                  outline: 'Blog structure and organization',
                  content: 'Full blog post generation',
                  metadata: 'SEO tags and descriptions'
                }

                return (
                  <div key={taskType} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-blue-300 dark:hover:border-blue-600 transition-colors">
                    <div className="flex items-center gap-2 mb-3">
                      <span className="text-xl">{taskIcons[taskType as keyof typeof taskIcons]}</span>
                      <label className="text-lg font-semibold capitalize">
                        {taskType} Model
                      </label>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                      {taskDescriptions[taskType as keyof typeof taskDescriptions]}
                    </p>
                    <select
                      value={selectedModel}
                      onChange={(e) => handleModelChange(taskType as keyof AISettings['models'], e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm"
                    >
                      {AI_MODELS[settings.provider].map((model) => (
                        <option key={model.id} value={model.id}>
                          {model.name} - ${model.costPer1kTokens}/1k tokens
                        </option>
                      ))}
                    </select>
                    <p className="text-xs text-gray-600 dark:text-gray-400 mt-2">
                      {AI_MODELS[settings.provider].find(m => m.id === selectedModel)?.description}
                    </p>
                  </div>
                )
              })}
            </div>
          </DashboardCardContent>
        </DashboardCard>
      )}

      {/* Generation Preferences */}
      <DashboardCard>
        <DashboardCardHeader>
          <DashboardCardTitle className="text-xl">Generation Preferences</DashboardCardTitle>
          <DashboardCardDescription>
            Configure generation parameters and limits
          </DashboardCardDescription>
        </DashboardCardHeader>
        <DashboardCardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <label className="flex items-center gap-2 text-sm font-semibold">
                🎯 Max Tokens per Request
              </label>
              <input
                type="number"
                min="100"
                max="100000"
                value={settings.preferences.maxTokensPerRequest}
                onChange={(e) => handlePreferenceChange('maxTokensPerRequest', parseInt(e.target.value))}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
              <p className="text-xs text-gray-600 dark:text-gray-400">
                Maximum tokens to use per AI request (100-100,000)
              </p>
            </div>

            <div className="space-y-2">
              <label className="flex items-center gap-2 text-sm font-semibold">
                🌡️ Temperature
              </label>
              <input
                type="number"
                min="0"
                max="2"
                step="0.1"
                value={settings.preferences.temperature}
                onChange={(e) => handlePreferenceChange('temperature', parseFloat(e.target.value))}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
              <p className="text-xs text-gray-600 dark:text-gray-400">
                Controls randomness: 0 = focused, 2 = creative (0.0-2.0)
              </p>
            </div>

            <div className="space-y-2">
              <label className="flex items-center gap-2 text-sm font-semibold">
                💰 Monthly Cost Limit (USD)
              </label>
              <input
                type="number"
                min="0"
                step="0.01"
                value={settings.preferences.costLimit}
                onChange={(e) => handlePreferenceChange('costLimit', parseFloat(e.target.value))}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
              <p className="text-xs text-gray-600 dark:text-gray-400">
                Maximum amount to spend on AI generation per month
              </p>
            </div>

            <div className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
              <div className="flex items-center gap-3">
                <input
                  type="checkbox"
                  id="enableCaching"
                  checked={settings.preferences.enableCaching}
                  onChange={(e) => handlePreferenceChange('enableCaching', e.target.checked)}
                  className="w-5 h-5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                />
                <div>
                  <label htmlFor="enableCaching" className="text-sm font-semibold flex items-center gap-2">
                    ⚡ Enable Caching
                  </label>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    Cache responses to reduce costs and improve speed
                  </p>
                </div>
              </div>
              <InformationCircleIcon className="w-5 h-5 text-gray-400" />
            </div>
          </div>
        </DashboardCardContent>
      </DashboardCard>
    </div>
  )
}
