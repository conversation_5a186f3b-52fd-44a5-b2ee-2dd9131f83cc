import { NextRequest, NextResponse } from 'next/server'
import { getUserAISettings, saveAISettings } from '@/lib/firebase-server'
import { DEFAULT_AI_SETTINGS } from '@/types/ai'

const ADMIN_UID = 'KNlrg408xubJeEmwFpUbeDQWBgF3'

export async function GET(request: NextRequest) {
  try {
    console.log('Testing AI settings API...')
    
    // Test getting AI settings for admin user
    const settings = await getUserAISettings(ADMIN_UID)
    console.log('AI settings retrieved:', settings ? 'Found' : 'Not found')
    
    if (!settings) {
      console.log('No settings found, creating default settings...')
      
      // Create default settings
      const settingsId = await saveAISettings(ADMIN_UID, DEFAULT_AI_SETTINGS)
      console.log('Default settings created with ID:', settingsId)
      
      // Retrieve the created settings
      const newSettings = await getUserAISettings(ADMIN_UID)
      console.log('New settings retrieved:', newSettings ? 'Success' : 'Failed')
      
      return NextResponse.json({
        success: true,
        message: 'Default AI settings created',
        settingsId,
        settings: newSettings
      })
    }
    
    return NextResponse.json({
      success: true,
      message: 'AI settings found',
      settings
    })
  } catch (error) {
    console.error('Error in test-ai-settings API:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to test AI settings',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
