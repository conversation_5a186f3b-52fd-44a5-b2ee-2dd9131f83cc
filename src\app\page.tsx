'use client'

import { useEffect, useState } from 'react'
import { getBlogPosts } from '@/lib/firebase-operations'
import { processMarkdownContent } from '@/lib/markdown-client'
import BlogGridWithFilter from '@/components/BlogGridWithFilter'
import { BlogPost, DatabaseBlogPost } from '@/types'

// Convert Firebase posts to BlogPost format
async function convertFirebasePostToBlogPost(firebasePost: DatabaseBlogPost): Promise<BlogPost> {
  // Process markdown content
  const processedContent = await processMarkdownContent(firebasePost.content)

  return {
    slug: firebasePost.slug,
    title: firebasePost.title,
    excerpt: firebasePost.excerpt,
    date: firebasePost.scheduled_for || firebasePost.created_at,
    featuredImage: firebasePost.featured_image || '/images/blog/default.png',
    content: processedContent,
    readTime: firebasePost.reading_time || 5,
    tags: firebasePost.tags || [],
    author: '<PERSON>',
    categories: firebasePost.categories || [],
  }
}

export default function Home() {
  const [posts, setPosts] = useState<BlogPost[]>([])
  const [loading, setLoading] = useState(true)
  const [debugInfo, setDebugInfo] = useState<string[]>(['Starting...'])

  useEffect(() => {
    async function loadPosts() {
      const addDebug = (msg: string) => {
        console.log(msg)
        setDebugInfo(prev => [...prev, msg])
      }

      try {
        addDebug('Loading posts using server-side API...')

        // Use the server-side API that we know works
        const response = await fetch('/api/test-posts')
        addDebug(`API response status: ${response.status}`)

        if (!response.ok) {
          addDebug(`API request failed: ${response.status} ${response.statusText}`)
          setPosts([])
          setLoading(false)
          return
        }

        const data = await response.json()
        addDebug(`API response received: ${data.success ? 'success' : 'failed'}`)

        if (!data.success) {
          addDebug(`API returned error: ${data.error}`)
          setPosts([])
          setLoading(false)
          return
        }

        const firebasePosts = data.posts as DatabaseBlogPost[]
        addDebug(`Server-side posts loaded: ${firebasePosts.length} posts`)

        if (firebasePosts.length === 0) {
          addDebug('No posts found in database')
          setPosts([])
          setLoading(false)
          return
        }

        // Convert Firebase posts to BlogPost format and filter published posts
        const publishedPosts = firebasePosts.filter(post => post.published)
        addDebug(`Published posts: ${publishedPosts.length}`)

        if (publishedPosts.length === 0) {
          addDebug('No published posts found')
          setPosts([])
          setLoading(false)
          return
        }

        addDebug('Converting posts...')
        const convertedPosts = await Promise.all(
          publishedPosts.map(post => convertFirebasePostToBlogPost(post))
        )
        addDebug(`Converted posts: ${convertedPosts.length}`)

        const sortedPosts = convertedPosts.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
        addDebug(`Sorted posts: ${sortedPosts.length}`)

        setPosts(sortedPosts)
        addDebug('Posts set successfully!')
      } catch (error) {
        addDebug(`Error loading posts: ${error}`)
        setPosts([])
      } finally {
        setLoading(false)
      }
    }

    loadPosts()
  }, [])

  return (
    <div className="relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section */}
        <div className="text-center py-20 mb-16">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            <span className="bg-gradient-to-r from-blue-500 via-blue-400 to-orange-400 dark:from-blue-400 dark:via-cyan-300 dark:to-orange-300 bg-clip-text text-transparent drop-shadow-sm">
              AI Automation, Website and App Development Expert Solutions
            </span>
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Thoughts on web development, AI automation, and the intersection of technology and creativity.
          </p>
        </div>

        {/* Blog Grid with Filter */}
        <div className="py-12 mb-20">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <p className="ml-4 text-gray-600 dark:text-gray-400">Loading blog posts...</p>
            </div>
          ) : posts.length === 0 ? (
            <div className="text-center py-12">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">No Blog Posts Found</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-8">
                There are currently no published blog posts available.
              </p>
              <div className="text-sm text-gray-500 dark:text-gray-500 max-w-2xl mx-auto">
                <p className="mb-4">Debug info:</p>
                <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg text-left">
                  {debugInfo.map((info, index) => (
                    <div key={index} className="mb-1 font-mono text-xs">
                      {index + 1}. {info}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <BlogGridWithFilter posts={posts} />
          )}
        </div>
      </div>
    </div>
  )
}
