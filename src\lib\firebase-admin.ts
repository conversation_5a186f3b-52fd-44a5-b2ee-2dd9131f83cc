import { initializeApp, getApps, cert } from 'firebase-admin/app'
import { getAuth } from 'firebase-admin/auth'

// Initialize Firebase Admin SDK
if (!getApps().length) {
  try {
    const projectId = process.env.FIREBASE_PROJECT_ID || process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID
    const clientEmail = process.env.FIREBASE_CLIENT_EMAIL
    const privateKey = process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n')

    console.log('Firebase Admin initialization:', {
      projectId: projectId ? 'present' : 'missing',
      clientEmail: clientEmail ? 'present' : 'missing',
      privateKey: privateKey ? 'present' : 'missing'
    })

    if (projectId && clientEmail && privateKey) {
      initializeApp({
        credential: cert({
          projectId,
          clientEmail,
          privateKey,
        }),
      })
      console.log('Firebase Admin SDK initialized successfully')
    } else {
      console.warn('Missing Firebase Admin credentials, initializing with project ID only')
      // Initialize with just project ID for basic functionality
      if (projectId) {
        initializeApp({
          projectId,
        })
        console.log('Firebase Admin SDK initialized with project ID only')
      } else {
        console.error('No Firebase project ID available')
      }
    }
  } catch (error) {
    console.error('Firebase admin initialization failed:', error)
  }
}

let auth: any = null

try {
  auth = getAuth()
  console.log('Firebase Admin Auth initialized successfully')
} catch (error) {
  console.error('Firebase Admin Auth not available:', error)
}

export async function verifyIdToken(idToken: string) {
  if (!auth) {
    throw new Error('Firebase Admin Auth not initialized')
  }
  
  try {
    const decodedToken = await auth.verifyIdToken(idToken)
    return decodedToken
  } catch (error) {
    throw new Error('Invalid token')
  }
}

export { auth }
