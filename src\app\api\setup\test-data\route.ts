import { NextRequest, NextResponse } from 'next/server'
import { collection, addDoc, serverTimestamp } from 'firebase/firestore'
import { db } from '@/lib/firebase'

export async function POST(request: NextRequest) {
  try {
    // Create a test blog post
    const testPost = {
      title: "Welcome to My Blog",
      content: `# Welcome to My Blog

This is a test blog post to verify that the Firebase integration is working correctly.

## About This Blog

This blog covers topics including:
- Web Development
- AI Automation
- Modern JavaScript Frameworks
- Firebase Integration

## Getting Started

Thank you for visiting my blog. More content will be added soon!

---

*This is a test post created automatically.*`,
      excerpt: "Welcome to my blog! This is a test post to verify Firebase integration is working correctly.",
      published: true,
      featured_image: "/images/blog/default.png",
      tags: ["welcome", "test", "firebase"],
      categories: ["General"],
      author_id: "KNlrg408xubJeEmwFpUbeDQWBgF3", // Admin UID
      slug: "welcome-to-my-blog",
      reading_time: 2,
      created_at: serverTimestamp(),
      updated_at: serverTimestamp(),
      scheduled_for: new Date().toISOString()
    }

    console.log('Creating test blog post...')
    const docRef = await addDoc(collection(db, 'blog_posts'), testPost)
    console.log('Test blog post created with ID:', docRef.id)

    return NextResponse.json({
      success: true,
      message: 'Test blog post created successfully',
      postId: docRef.id
    })
  } catch (error) {
    console.error('Error creating test post:', error)
    return NextResponse.json(
      { 
        error: 'Failed to create test post',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
