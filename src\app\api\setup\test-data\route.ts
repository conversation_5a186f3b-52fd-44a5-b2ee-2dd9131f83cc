import { NextRequest, NextResponse } from 'next/server'
import { getFirestore } from 'firebase-admin/firestore'
import { initializeApp, getApps, cert } from 'firebase-admin/app'

// Initialize Firebase Admin if not already initialized
if (!getApps().length) {
  try {
    const projectId = process.env.FIREBASE_PROJECT_ID
    const clientEmail = process.env.FIREBASE_CLIENT_EMAIL
    const privateKey = process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n')

    if (projectId && clientEmail && privateKey) {
      initializeApp({
        credential: cert({
          projectId,
          clientEmail,
          privateKey,
        }),
      })
    }
  } catch (error) {
    console.error('Firebase admin initialization error:', error)
  }
}

export async function POST(request: NextRequest) {
  try {
    const db = getFirestore()

    // Check if test posts already exist
    const existingPosts = await db.collection('blog_posts')
      .where('slug', '==', 'welcome-to-my-blog')
      .get()

    if (!existingPosts.empty) {
      return NextResponse.json({
        success: true,
        message: 'Test data already exists',
        postId: existingPosts.docs[0].id
      })
    }

    // Create multiple test blog posts using admin SDK
    const testPosts = [
      {
        title: "Welcome to My Blog",
        content: `# Welcome to My Blog

This is a test blog post to verify that the Firebase integration is working correctly.

## About This Blog

This blog covers topics including:
- Web Development
- AI Automation
- Modern JavaScript Frameworks
- Firebase Integration

## Getting Started

Thank you for visiting my blog. More content will be added soon!

---

*This is a test post created automatically.*`,
        excerpt: "Welcome to my blog! This is a test post to verify Firebase integration is working correctly.",
        published: true,
        featured_image: "/images/blog/default.png",
        tags: ["welcome", "test", "firebase"],
        categories: ["General"],
        author_id: "KNlrg408xubJeEmwFpUbeDQWBgF3", // Admin UID
        slug: "welcome-to-my-blog",
        reading_time: 2,
        created_at: new Date(),
        updated_at: new Date(),
        scheduled_for: new Date().toISOString()
      },
      {
        title: "Getting Started with AI Automation",
        content: `# Getting Started with AI Automation

AI automation is revolutionizing how we work and build applications.

## What is AI Automation?

AI automation combines artificial intelligence with automated processes to:
- Reduce manual work
- Improve accuracy
- Scale operations
- Enhance user experiences

## Key Benefits

1. **Efficiency**: Automate repetitive tasks
2. **Accuracy**: Reduce human error
3. **Scalability**: Handle increasing workloads
4. **Innovation**: Focus on creative work

## Getting Started

Start small with simple automation tasks and gradually build more complex systems.

---

*Learn more about AI automation in upcoming posts.*`,
        excerpt: "Discover how AI automation can transform your workflow and improve efficiency.",
        slug: "getting-started-with-ai-automation",
        author_id: "KNlrg408xubJeEmwFpUbeDQWBgF3",
        published: true,
        featured_image: "/images/blog/default.png",
        tags: ["ai", "automation", "productivity"],
        categories: ["AI", "Technology"],
        reading_time: 3,
        created_at: new Date(),
        updated_at: new Date(),
        scheduled_for: new Date().toISOString()
      },
      {
        title: "Modern Web Development with React and Next.js",
        content: `# Modern Web Development with React and Next.js

Building modern web applications requires the right tools and frameworks.

## Why React and Next.js?

React and Next.js provide:
- Component-based architecture
- Server-side rendering
- Excellent performance
- Great developer experience

## Key Features

### React
- Virtual DOM for performance
- Reusable components
- Strong ecosystem

### Next.js
- File-based routing
- API routes
- Image optimization
- Built-in CSS support

## Best Practices

1. Use TypeScript for type safety
2. Implement proper error boundaries
3. Optimize for performance
4. Follow accessibility guidelines

---

*More web development tips coming soon.*`,
        excerpt: "Learn about modern web development using React and Next.js frameworks.",
        slug: "modern-web-development-react-nextjs",
        author_id: "KNlrg408xubJeEmwFpUbeDQWBgF3",
        published: true,
        featured_image: "/images/blog/default.png",
        tags: ["react", "nextjs", "web development", "javascript"],
        categories: ["Web Development", "React"],
        reading_time: 4,
        created_at: new Date(),
        updated_at: new Date(),
        scheduled_for: new Date().toISOString()
      }
    ]

    console.log('Creating test blog posts using admin SDK...')
    const createdPosts = []

    for (const post of testPosts) {
      const docRef = await db.collection('blog_posts').add(post)
      createdPosts.push({ id: docRef.id, title: post.title })
      console.log(`Created post: ${post.title} with ID: ${docRef.id}`)
    }

    return NextResponse.json({
      success: true,
      message: 'Test data created successfully',
      posts: createdPosts
    })
  } catch (error) {
    console.error('Error creating test posts:', error)
    return NextResponse.json(
      {
        error: 'Failed to create test posts',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
