rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Blog posts - only authenticated users can manage their own posts
    match /blog_posts/{postId} {
      allow read: if true; // Anyone can read published posts (you can add published check)
      allow write, delete: if request.auth != null && request.auth.uid == resource.data.author_id;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.author_id;
    }
    
    // Projects - only authenticated users can manage their own projects
    match /projects/{projectId} {
      allow read: if true; // Anyone can read published projects
      allow write, delete: if request.auth != null && request.auth.uid == resource.data.author_id;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.author_id;
    }
    
    // Uploaded files - only authenticated users can manage their own files
    match /uploaded_files/{fileId} {
      allow read, write, delete: if request.auth != null && request.auth.uid == resource.data.uploaded_by;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.uploaded_by;
    }
    
    // User profiles (optional - for future use)
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Admin profile - allow read for everyone, write only for admin
    match /admin_profile/{profileId} {
      allow read: if true; // Anyone can read admin profile for avatar display
      allow write: if request.auth != null && request.auth.uid == 'KNlrg408xubJeEmwFpUbeDQWBgF3';
    }

    // Comments - allow reading all comments for authenticated users, approved for public
    match /comments/{commentId} {
      // Authenticated users can read all comments (for admin dashboard), public can read approved
      allow read: if request.auth != null || resource.data.status == 'approved';

      // Authenticated users can create comments
      allow create: if request.auth != null;

      // Authenticated users can update and delete comments (for now - we'll restrict this later)
      allow update, delete: if request.auth != null;
    }

    // AI Settings - users can only manage their own AI settings
    match /ai_settings/{settingsId} {
      allow read, write, delete: if request.auth != null && request.auth.uid == resource.data.user_id;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.user_id;
    }

    // AI Cache - users can only access their own cache entries
    match /ai_cache/{cacheId} {
      allow read, write, delete: if request.auth != null && request.auth.uid == resource.data.user_id;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.user_id;
    }

    // AI Token Usage - users can only access their own usage data
    match /token_usage/{usageId} {
      allow read, write, delete: if request.auth != null && request.auth.uid == resource.data.user_id;
      allow create: if request.auth != null && request.auth.uid == request.resource.data.user_id;
    }
  }
}
