const admin = require('firebase-admin');

// Initialize Firebase Admin
const serviceAccount = {
  type: "service_account",
  project_id: "ernestromelo-blog",
  private_key_id: "fbsvc",
**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  client_email: "<EMAIL>",
  client_id: "116234567890123456789",
  auth_uri: "https://accounts.google.com/o/oauth2/auth",
  token_uri: "https://oauth2.googleapis.com/token",
  auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
  client_x509_cert_url: "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-fbsvc%40ernestromelo-blog.iam.gserviceaccount.com"
};

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  projectId: 'ernestromelo-blog'
});

const db = admin.firestore();

async function addTestPost() {
  try {
    const testPost = {
      title: "Welcome to My Blog",
      content: "# Welcome to My Blog\n\nThis is a test blog post to verify that the blog functionality is working correctly.\n\n## Features\n\n- Markdown support\n- Firebase integration\n- Responsive design\n\nThank you for visiting!",
      excerpt: "This is a test blog post to verify that the blog functionality is working correctly.",
      slug: "welcome-to-my-blog",
      author_id: "KNlrg408xubJeEmwFpUbeDQWBgF3",
      published: true,
      featured_image: "https://images.unsplash.com/photo-*************-ce68d2c6f44d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2072&q=80",
      categories: ["Technology", "Web Development"],
      tags: ["blog", "welcome", "test"],
      reading_time: 2,
      created_at: admin.firestore.FieldValue.serverTimestamp(),
      updated_at: admin.firestore.FieldValue.serverTimestamp(),
      scheduled_for: null
    };

    const docRef = await db.collection('blog_posts').add(testPost);
    console.log('Test post added with ID:', docRef.id);
    
    // Add another test post
    const testPost2 = {
      title: "Getting Started with Firebase",
      content: "# Getting Started with Firebase\n\nFirebase is a powerful platform for building web and mobile applications.\n\n## Key Features\n\n- Real-time database\n- Authentication\n- Cloud storage\n- Hosting\n\nLearn how to integrate Firebase into your projects.",
      excerpt: "Learn how to get started with Firebase for your web and mobile applications.",
      slug: "getting-started-with-firebase",
      author_id: "KNlrg408xubJeEmwFpUbeDQWBgF3",
      published: true,
      featured_image: "https://images.unsplash.com/photo-1551650975-87deedd944c3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2074&q=80",
      categories: ["Technology", "Firebase"],
      tags: ["firebase", "database", "tutorial"],
      reading_time: 3,
      created_at: admin.firestore.FieldValue.serverTimestamp(),
      updated_at: admin.firestore.FieldValue.serverTimestamp(),
      scheduled_for: null
    };

    const docRef2 = await db.collection('blog_posts').add(testPost2);
    console.log('Second test post added with ID:', docRef2.id);
    
    console.log('Test posts added successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error adding test posts:', error);
    process.exit(1);
  }
}

addTestPost();
