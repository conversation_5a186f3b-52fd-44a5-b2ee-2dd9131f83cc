import { initializeApp, getApps, cert } from 'firebase-admin/app'
import { getFirestore } from 'firebase-admin/firestore'
import { DatabaseBlogPost, DatabaseProject } from '@/types'

// Initialize Firebase Admin SDK
if (!getApps().length) {
  try {
    const projectId = process.env.FIREBASE_PROJECT_ID
    const clientEmail = process.env.FIREBASE_CLIENT_EMAIL
    const privateKey = process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n')

    console.log('Firebase Server initialization:', {
      projectId: projectId ? 'present' : 'missing',
      clientEmail: clientEmail ? 'present' : 'missing',
      privateKey: privateKey ? 'present' : 'missing'
    })

    if (projectId && clientEmail && privateKey) {
      initializeApp({
        credential: cert({
          projectId,
          clientEmail,
          privateKey,
        }),
      })
      console.log('Firebase Server SDK initialized successfully')
    } else {
      console.error('Missing Firebase Server credentials')
    }
  } catch (error) {
    console.error('Firebase server initialization error:', error)
  }
}

let db: any = null

try {
  db = getFirestore()
  console.log('Firebase Server Firestore initialized successfully')
} catch (error) {
  console.error('Firestore not available:', error)
}

// Server-side blog operations
export async function getBlogPosts(): Promise<DatabaseBlogPost[]> {
  if (!db) {
    console.warn('Firestore not initialized')
    return []
  }

  try {
    const snapshot = await db
      .collection('blog_posts')
      .where('published', '==', true)
      .orderBy('created_at', 'desc')
      .get()

    return snapshot.docs.map(doc => {
      const data = doc.data()
      return {
        id: doc.id,
        ...data,
        created_at: data.created_at?.toDate?.()?.toISOString() || new Date().toISOString(),
        updated_at: data.updated_at?.toDate?.()?.toISOString() || new Date().toISOString(),
      } as DatabaseBlogPost
    })
  } catch (error) {
    console.error('Error getting blog posts (server):', error)
    return []
  }
}

export async function getBlogPostBySlug(slug: string): Promise<DatabaseBlogPost | null> {
  if (!db) {
    console.warn('Firestore not initialized')
    return null
  }

  try {
    const snapshot = await db
      .collection('blog_posts')
      .where('slug', '==', slug)
      .where('published', '==', true)
      .limit(1)
      .get()

    if (snapshot.empty) {
      return null
    }

    const doc = snapshot.docs[0]
    const data = doc.data()

    return {
      id: doc.id,
      ...data,
      created_at: data.created_at?.toDate?.()?.toISOString() || new Date().toISOString(),
      updated_at: data.updated_at?.toDate?.()?.toISOString() || new Date().toISOString(),
    } as DatabaseBlogPost
  } catch (error) {
    console.error('Error getting blog post by slug (server):', error)
    return null
  }
}

export async function getProjects(): Promise<DatabaseProject[]> {
  if (!db) {
    console.warn('Firestore not initialized')
    return []
  }

  try {
    const snapshot = await db
      .collection('projects')
      .where('published', '==', true)
      .orderBy('created_at', 'desc')
      .get()

    return snapshot.docs.map(doc => {
      const data = doc.data()
      return {
        id: doc.id,
        ...data,
        created_at: data.created_at?.toDate?.()?.toISOString() || new Date().toISOString(),
        updated_at: data.updated_at?.toDate?.()?.toISOString() || new Date().toISOString(),
      } as DatabaseProject
    })
  } catch (error) {
    console.error('Error getting projects (server):', error)
    return []
  }
}

// Server-side AI settings operations
export async function getUserAISettings(userId: string): Promise<any | null> {
  if (!db) {
    console.warn('Firestore not initialized')
    return null
  }

  try {
    const settingsCollection = db.collection('ai_settings')
    const snapshot = await settingsCollection.where('user_id', '==', userId).get()

    if (snapshot.empty) {
      return null
    }

    const doc = snapshot.docs[0]
    const data = doc.data()
    return {
      id: doc.id,
      ...data,
      created_at: data.created_at?.toDate?.()?.toISOString() || new Date().toISOString(),
      updated_at: data.updated_at?.toDate?.()?.toISOString() || new Date().toISOString(),
    }
  } catch (error) {
    console.error('Error getting AI settings (server):', error)
    return null
  }
}

export async function saveAISettings(userId: string, settings: any): Promise<string> {
  if (!db) {
    throw new Error('Firestore not initialized')
  }

  try {
    const settingsCollection = db.collection('ai_settings')

    // Check if settings already exist for this user
    const existingSnapshot = await settingsCollection.where('user_id', '==', userId).get()

    const settingsData = {
      ...settings,
      user_id: userId,
      updated_at: new Date(),
    }

    if (!existingSnapshot.empty) {
      // Update existing settings
      const docRef = existingSnapshot.docs[0].ref
      await docRef.update(settingsData)
      return docRef.id
    } else {
      // Create new settings
      settingsData.created_at = new Date()
      const docRef = await settingsCollection.add(settingsData)
      return docRef.id
    }
  } catch (error) {
    console.error('Error saving AI settings (server):', error)
    throw error
  }
}
