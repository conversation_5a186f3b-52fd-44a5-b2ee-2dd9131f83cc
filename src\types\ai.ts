// AI Provider Types and Interfaces

export type AIProvider = 'openai' | 'openrouter' | 'gemini' | 'anthropic' | 'cohere'

export type TaskType = 'research' | 'outline' | 'content' | 'metadata' | 'image'

export type ImageProvider = 'openai' | 'midjourney' | 'stability' | 'leonardo'

export interface AIModel {
  id: string
  name: string
  provider: AIProvider
  costPer1kTokens: number
  maxTokens: number
  description: string
  recommended: TaskType[]
}

export interface AIProviderConfig {
  provider: AIProvider
  apiKey: string
  baseUrl?: string
  models: AIModel[]
}

export interface ImageModel {
  id: string
  name: string
  provider: ImageProvider
  costPerImage: number
  maxResolution: string
  description: string
  supportedStyles: string[]
}

export interface AISettings {
  id?: string
  user_id: string
  provider: AIProvider
  models: {
    research: string
    outline: string
    content: string
    metadata: string
    image?: string
  }
  imageProvider?: ImageProvider
  preferences: {
    maxTokensPerRequest: number
    temperature: number
    enableCaching: boolean
    costLimit: number // in USD
    imageGeneration?: {
      defaultStyle: string
      defaultResolution: string
      enableImageGen: boolean
    }
  }
  created_at: string
  updated_at: string
}

export interface AIRequest {
  provider: AIProvider
  model: string
  prompt: string
  maxTokens?: number
  temperature?: number
  systemPrompt?: string
}

export interface AIResponse {
  content: string
  tokensUsed: number
  cost: number
  provider: AIProvider
  model: string
  cached: boolean
}

export interface ImageRequest {
  provider: ImageProvider
  model: string
  prompt: string
  style?: string
  resolution?: string
  quality?: 'standard' | 'hd'
  count?: number
}

export interface ImageResponse {
  images: Array<{
    url: string
    revisedPrompt?: string
  }>
  cost: number
  provider: ImageProvider
  model: string
}

export interface ResearchResult {
  query: string
  sources: ResearchSource[]
  summary: string
  keyPoints: string[]
  relatedTopics: string[]
  tokensUsed: number
  cost: number
}

export interface ResearchSource {
  url: string
  title: string
  content: string
  relevanceScore: number
  datePublished?: string
  author?: string
  domain: string
}

export interface BlogOutline {
  title: string
  introduction: OutlineSection
  chapters: OutlineChapter[]
  conclusion: OutlineSection
  metadata: {
    estimatedWordCount: number
    targetAudience: string
    seoKeywords: string[]
    categories: string[]
    tags: string[]
  }
  internalLinks: InternalLink[]
  externalSources: ResearchSource[]
}

export interface OutlineSection {
  title: string
  content: string
  keyPoints: string[]
  wordCount: number
}

export interface OutlineChapter {
  title: string
  sections: OutlineSection[]
  includeTable: boolean
  includeQuote: boolean
  includeCodeBlock: boolean
  estimatedWordCount: number
}

export interface InternalLink {
  text: string
  url: string
  relevanceScore: number
  context: string
}

export interface GenerationProgress {
  stage: 'research' | 'outline' | 'content' | 'complete'
  currentChapter?: number
  totalChapters?: number
  progress: number // 0-100
  message: string
  tokensUsed: number
  estimatedCost: number
}

export interface TokenUsage {
  provider: AIProvider
  model: string
  promptTokens: number
  completionTokens: number
  totalTokens: number
  cost: number
  timestamp: string
}

export interface CacheEntry {
  key: string
  content: string
  provider: AIProvider
  model: string
  expiresAt: string
  tokensUsed: number
}

// Available AI Models Configuration
export const AI_MODELS: Record<AIProvider, AIModel[]> = {
  openai: [
    {
      id: 'gpt-3.5-turbo',
      name: 'GPT-3.5 Turbo',
      provider: 'openai',
      costPer1kTokens: 0.002,
      maxTokens: 4096,
      description: 'Fast and cost-effective for basic tasks',
      recommended: ['research', 'metadata']
    },
    {
      id: 'gpt-4o-mini',
      name: 'GPT-4o Mini',
      provider: 'openai',
      costPer1kTokens: 0.15,
      maxTokens: 128000,
      description: 'Balanced performance and cost',
      recommended: ['outline', 'content']
    },
    {
      id: 'gpt-4o',
      name: 'GPT-4o',
      provider: 'openai',
      costPer1kTokens: 5.0,
      maxTokens: 128000,
      description: 'Highest quality for complex content',
      recommended: ['content']
    },
    {
      id: 'gpt-4-turbo',
      name: 'GPT-4 Turbo',
      provider: 'openai',
      costPer1kTokens: 10.0,
      maxTokens: 128000,
      description: 'Advanced reasoning and analysis',
      recommended: ['content', 'outline']
    },
    {
      id: 'o3-mini',
      name: 'o3-mini',
      provider: 'openai',
      costPer1kTokens: 3.0,
      maxTokens: 200000,
      description: 'Latest reasoning model, cost-effective',
      recommended: ['outline', 'content']
    },
    {
      id: 'o3-mini-relaxed',
      name: 'o3-mini (Relaxed)',
      provider: 'openai',
      costPer1kTokens: 1.5,
      maxTokens: 200000,
      description: 'o3-mini with relaxed reasoning for faster responses',
      recommended: ['research', 'outline']
    },
    {
      id: 'o3',
      name: 'o3',
      provider: 'openai',
      costPer1kTokens: 15.0,
      maxTokens: 200000,
      description: 'Most advanced reasoning model for complex tasks',
      recommended: ['content']
    }
  ],
  gemini: [
    {
      id: 'gemini-1.5-flash',
      name: 'Gemini 1.5 Flash',
      provider: 'gemini',
      costPer1kTokens: 0.00015,
      maxTokens: 1000000,
      description: 'Ultra-fast and cost-effective',
      recommended: ['research', 'metadata']
    },
    {
      id: 'gemini-1.5-flash-8b',
      name: 'Gemini 1.5 Flash 8B',
      provider: 'gemini',
      costPer1kTokens: 0.0001,
      maxTokens: 1000000,
      description: 'Extremely fast and economical',
      recommended: ['research', 'metadata']
    },
    {
      id: 'gemini-1.5-flash-002',
      name: 'Gemini 1.5 Flash-002',
      provider: 'gemini',
      costPer1kTokens: 0.00015,
      maxTokens: 1000000,
      description: 'Latest Flash model with improved performance',
      recommended: ['research', 'outline']
    },
    {
      id: 'gemini-1.5-pro',
      name: 'Gemini 1.5 Pro',
      provider: 'gemini',
      costPer1kTokens: 0.00125,
      maxTokens: 2000000,
      description: 'High-quality reasoning and generation',
      recommended: ['outline', 'content']
    },
    {
      id: 'gemini-1.5-pro-002',
      name: 'Gemini 1.5 Pro-002',
      provider: 'gemini',
      costPer1kTokens: 0.00125,
      maxTokens: 2000000,
      description: 'Latest Pro model with enhanced capabilities',
      recommended: ['content']
    },
    {
      id: 'gemini-2.0-flash-exp',
      name: 'Gemini 2.0 Flash (Experimental)',
      provider: 'gemini',
      costPer1kTokens: 0.0002,
      maxTokens: 1000000,
      description: 'Next-generation Flash model (experimental)',
      recommended: ['research', 'outline']
    }
  ],
  openrouter: [
    {
      id: 'meta-llama/llama-3.1-8b-instruct',
      name: 'Llama 3.1 8B',
      provider: 'openrouter',
      costPer1kTokens: 0.0002,
      maxTokens: 128000,
      description: 'Open source, very cost-effective',
      recommended: ['research', 'metadata']
    },
    {
      id: 'meta-llama/llama-3.1-70b-instruct',
      name: 'Llama 3.1 70B',
      provider: 'openrouter',
      costPer1kTokens: 0.0009,
      maxTokens: 128000,
      description: 'Powerful open source model',
      recommended: ['content', 'outline']
    },
    {
      id: 'anthropic/claude-3.5-sonnet',
      name: 'Claude 3.5 Sonnet',
      provider: 'openrouter',
      costPer1kTokens: 3.0,
      maxTokens: 200000,
      description: 'Excellent for long-form content',
      recommended: ['content']
    },
    {
      id: 'anthropic/claude-3-haiku',
      name: 'Claude 3 Haiku',
      provider: 'openrouter',
      costPer1kTokens: 0.25,
      maxTokens: 200000,
      description: 'Fast and efficient Claude model',
      recommended: ['research', 'outline']
    },
    {
      id: 'openai/gpt-4o-mini',
      name: 'GPT-4o Mini (OpenRouter)',
      provider: 'openrouter',
      costPer1kTokens: 0.15,
      maxTokens: 128000,
      description: 'GPT-4o Mini via OpenRouter',
      recommended: ['outline', 'content']
    },
    {
      id: 'mistralai/mistral-7b-instruct',
      name: 'Mistral 7B Instruct',
      provider: 'openrouter',
      costPer1kTokens: 0.0001,
      maxTokens: 32000,
      description: 'Efficient European AI model',
      recommended: ['research', 'metadata']
    }
  ],
  anthropic: [
    {
      id: 'claude-3-5-sonnet-20241022',
      name: 'Claude 3.5 Sonnet',
      provider: 'anthropic',
      costPer1kTokens: 3.0,
      maxTokens: 200000,
      description: 'Latest Claude model with enhanced capabilities',
      recommended: ['content', 'outline']
    },
    {
      id: 'claude-3-haiku-20240307',
      name: 'Claude 3 Haiku',
      provider: 'anthropic',
      costPer1kTokens: 0.25,
      maxTokens: 200000,
      description: 'Fast and cost-effective Claude model',
      recommended: ['research', 'metadata']
    }
  ],
  cohere: [
    {
      id: 'command-r-plus',
      name: 'Command R+',
      provider: 'cohere',
      costPer1kTokens: 3.0,
      maxTokens: 128000,
      description: 'Advanced reasoning and generation',
      recommended: ['content', 'outline']
    },
    {
      id: 'command-r',
      name: 'Command R',
      provider: 'cohere',
      costPer1kTokens: 0.5,
      maxTokens: 128000,
      description: 'Balanced performance model',
      recommended: ['research', 'outline']
    }
  ]
}

// Available Image Generation Models
export const IMAGE_MODELS: Record<ImageProvider, ImageModel[]> = {
  openai: [
    {
      id: 'dall-e-3',
      name: 'DALL-E 3',
      provider: 'openai',
      costPerImage: 0.04,
      maxResolution: '1024x1024',
      description: 'Latest DALL-E with improved quality and prompt adherence',
      supportedStyles: ['natural', 'vivid']
    },
    {
      id: 'dall-e-2',
      name: 'DALL-E 2',
      provider: 'openai',
      costPerImage: 0.02,
      maxResolution: '1024x1024',
      description: 'Previous generation DALL-E, cost-effective',
      supportedStyles: ['natural']
    }
  ],
  midjourney: [
    {
      id: 'midjourney-v6',
      name: 'Midjourney v6',
      provider: 'midjourney',
      costPerImage: 0.08,
      maxResolution: '2048x2048',
      description: 'Latest Midjourney with photorealistic capabilities',
      supportedStyles: ['photorealistic', 'artistic', 'anime', 'abstract']
    }
  ],
  stability: [
    {
      id: 'stable-diffusion-xl',
      name: 'Stable Diffusion XL',
      provider: 'stability',
      costPerImage: 0.03,
      maxResolution: '1024x1024',
      description: 'High-quality open source image generation',
      supportedStyles: ['photorealistic', 'artistic', 'digital-art']
    },
    {
      id: 'stable-diffusion-3',
      name: 'Stable Diffusion 3',
      provider: 'stability',
      costPerImage: 0.05,
      maxResolution: '1536x1536',
      description: 'Latest Stable Diffusion with improved text rendering',
      supportedStyles: ['photorealistic', 'artistic', 'digital-art', 'concept-art']
    }
  ],
  leonardo: [
    {
      id: 'leonardo-diffusion-xl',
      name: 'Leonardo Diffusion XL',
      provider: 'leonardo',
      costPerImage: 0.02,
      maxResolution: '1024x1024',
      description: 'Creative AI with fine-tuned models',
      supportedStyles: ['photorealistic', 'artistic', 'fantasy', 'sci-fi']
    }
  ]
}

export const DEFAULT_AI_SETTINGS: Omit<AISettings, 'id' | 'user_id' | 'created_at' | 'updated_at'> = {
  provider: 'gemini',
  models: {
    research: 'gemini-1.5-flash',
    outline: 'gemini-1.5-pro',
    content: 'gemini-1.5-pro',
    metadata: 'gemini-1.5-flash'
  },
  preferences: {
    maxTokensPerRequest: 4000,
    temperature: 0.7,
    enableCaching: true,
    costLimit: 10.0 // $10 USD limit
  }
}
