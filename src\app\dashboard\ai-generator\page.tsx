'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/providers/AuthProvider'
import {
  SparklesIcon,
  CogIcon,
  DocumentTextIcon,
  MagnifyingGlassIcon,
  PencilIcon,
  RocketLaunchIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'
import {
  DashboardCard,
  DashboardCardContent,
  DashboardCardDescription,
  DashboardCardHeader,
  DashboardCardTitle
} from '@/components/dashboard/DashboardCard'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import Link from 'next/link'
import { getAIProviderStatus } from '@/lib/ai'
import { getTotalCostForUser, getUserAISettings, getProviderUsageStats, getMonthlyUsageData, getTopModels } from '@/lib/ai/settings'
import { AISettings } from '@/types/ai'
import { Line } from 'react-chartjs-2'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js'

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
)

export default function AIGeneratorPage() {
  const { user } = useAuth()
  const [aiSettings, setAiSettings] = useState<AISettings | null>(null)
  const [providerStatus, setProviderStatus] = useState({
    openai: false,
    gemini: false,
    openrouter: false
  })
  const [monthlyUsage, setMonthlyUsage] = useState(0)
  const [providerUsage, setProviderUsage] = useState<any>({})
  const [monthlyData, setMonthlyData] = useState<any>([])
  const [topModels, setTopModels] = useState<any>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user) {
      loadAIData()
    }
  }, [user])

  const loadAIData = async () => {
    if (!user) return

    try {
      const [settings, status, usage, providerStats, monthlyStats, topModelStats] = await Promise.all([
        getUserAISettings(user.uid),
        Promise.resolve(getAIProviderStatus()),
        getTotalCostForUser(user.uid, 30),
        getProviderUsageStats(user.uid),
        getMonthlyUsageData(user.uid),
        getTopModels(user.uid)
      ])

      setAiSettings(settings)
      setProviderStatus(status)
      setMonthlyUsage(usage)

      // Convert provider stats array to object for easier lookup
      const providerUsageObj: any = {}
      providerStats.forEach(stat => {
        providerUsageObj[stat.provider] = {
          tokens: stat.totalTokens,
          cost: stat.totalCost
        }
      })
      setProviderUsage(providerUsageObj)

      // Format monthly data for chart
      const formattedMonthlyData = monthlyStats.map(data => ({
        month: new Date(data.month + '-01').toLocaleDateString('en-US', { month: 'short', year: '2-digit' }),
        cost: data.totalCost
      }))
      setMonthlyData(formattedMonthlyData)

      // Format top models data
      const formattedTopModels = topModelStats.map(model => ({
        name: model.model,
        provider: model.provider,
        tokens: model.totalTokens,
        cost: model.totalCost
      }))
      setTopModels(formattedTopModels)
    } catch (error) {
      console.error('Error loading AI data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getProviderBadgeColor = (isAvailable: boolean) => {
    return isAvailable ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
  }

  const workflowSteps = [
    {
      step: 1,
      title: 'Keywords & Research',
      description: 'Enter keywords and let AI conduct deep research',
      icon: MagnifyingGlassIcon,
      href: '/dashboard/ai-generator/research',
      color: 'bg-blue-500'
    },
    {
      step: 2,
      title: 'Generate Outline',
      description: 'AI creates a comprehensive blog outline',
      icon: DocumentTextIcon,
      href: '/dashboard/ai-generator/outline',
      color: 'bg-purple-500'
    },
    {
      step: 3,
      title: 'Content Generation',
      description: 'Generate content in batches with rich formatting',
      icon: PencilIcon,
      href: '/dashboard/ai-generator/generate',
      color: 'bg-green-500'
    },
    {
      step: 4,
      title: 'Review & Publish',
      description: 'Review, edit, and publish your AI-generated blog',
      icon: RocketLaunchIcon,
      href: '/dashboard/ai-generator/review',
      color: 'bg-orange-500'
    }
  ]

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading AI Generator...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight dashboard-text flex items-center gap-3">
            <SparklesIcon className="w-8 h-8 text-blue-600" />
            AI Blog Generator
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2 text-lg">
            Generate high-quality blog posts with AI-powered research and content creation
          </p>
        </div>
        <Button asChild size="lg" className="bg-blue-600 hover:bg-blue-700 text-white">
          <Link href="/dashboard/ai-generator/settings">
            <CogIcon className="w-5 h-5 mr-2" />
            AI Settings
          </Link>
        </Button>
      </div>

      {/* Main Action - Start Generation */}
      <DashboardCard className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-blue-200 dark:border-blue-800">
        <DashboardCardContent className="p-8">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <SparklesIcon className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-2xl font-bold dashboard-text mb-2">Ready to Create?</h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
              Start your AI-powered blog generation journey. Research keywords, create outlines, and generate complete blog posts.
            </p>
            <Button asChild size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8">
              <Link href="/dashboard/ai-generator/research">
                <RocketLaunchIcon className="w-5 h-5 mr-2" />
                Start New Blog Generation
              </Link>
            </Button>
          </div>
        </DashboardCardContent>
      </DashboardCard>

      {/* Status and Settings */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* AI Providers */}
        <DashboardCard>
          <DashboardCardHeader>
            <DashboardCardTitle>AI Providers</DashboardCardTitle>
            <DashboardCardDescription>
              Token usage and generation costs by provider
            </DashboardCardDescription>
          </DashboardCardHeader>
          <DashboardCardContent>
            <div className="space-y-3">
              {Object.entries(providerStatus).map(([provider, available]) => {
                const usage = providerUsage[provider] || { tokens: 0, cost: 0 }
                return (
                  <div key={provider} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full ${available ? 'bg-green-500' : 'bg-gray-400'}`} />
                      <span className="text-sm font-medium capitalize">{provider}</span>
                    </div>
                    <div className="flex items-center gap-4">
                      {available && (
                        <>
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            {usage.tokens.toLocaleString()} tokens
                          </span>
                          <span className="text-sm font-semibold">
                            ${usage.cost.toFixed(2)}
                          </span>
                        </>
                      )}
                      <Badge variant={available ? "default" : "outline"}>
                        {available ? "Available" : "Not Available"}
                      </Badge>
                    </div>
                  </div>
                )
              })}
            </div>
          </DashboardCardContent>
        </DashboardCard>

        {/* Monthly Usage Graph */}
        <DashboardCard>
          <DashboardCardHeader>
            <DashboardCardTitle>Monthly Usage</DashboardCardTitle>
            <DashboardCardDescription>
              AI generation costs over time
            </DashboardCardDescription>
          </DashboardCardHeader>
          <DashboardCardContent>
            {monthlyData.length > 0 ? (
              <div className="h-56 flex items-end">
                <div className="w-full h-40">
                  <Line
                    data={{
                      labels: monthlyData.map((d: any) => d.month),
                      datasets: [
                        {
                          label: 'Cost ($)',
                          data: monthlyData.map((d: any) => d.cost),
                          borderColor: 'rgb(59, 130, 246)',
                          backgroundColor: 'rgba(59, 130, 246, 0.1)',
                          fill: true,
                          tension: 0.4,
                          borderWidth: 2,
                          pointRadius: 3,
                          pointHoverRadius: 5
                        }
                      ]
                    }}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          display: false
                        }
                      },
                      scales: {
                        y: {
                          beginAtZero: true,
                          grid: {
                            display: false
                          },
                          border: {
                            display: false
                          },
                          ticks: {
                            callback: function(value) {
                              return '$' + value
                            },
                            color: 'rgba(156, 163, 175, 0.8)',
                            font: {
                              size: 11
                            }
                          }
                        },
                        x: {
                          grid: {
                            display: false
                          },
                          border: {
                            display: false
                          },
                          ticks: {
                            color: 'rgba(156, 163, 175, 0.8)',
                            font: {
                              size: 11
                            }
                          }
                        }
                      }
                    }}
                  />
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  No usage data available yet
                </p>
              </div>
            )}
          </DashboardCardContent>
        </DashboardCard>

        {/* Top Models */}
        <DashboardCard>
          <DashboardCardHeader>
            <DashboardCardTitle>Top 3 Models</DashboardCardTitle>
            <DashboardCardDescription>
              Most used AI models this month
            </DashboardCardDescription>
          </DashboardCardHeader>
          <DashboardCardContent>
            <div className="grid grid-cols-2 gap-4">
              {/* Total Cost */}
              <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                <div className="text-2xl font-bold dashboard-text">
                  ${monthlyUsage.toFixed(2)}
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  Total Cost
                </p>
              </div>

              {/* Top Models */}
              <div>
                {topModels.length > 0 ? (
                  <div className="space-y-2">
                    {topModels.slice(0, 3).map((model: any, index: number) => (
                      <div key={model.name} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                            index === 0 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' :
                            index === 1 ? 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300' :
                            'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300'
                          }`}>
                            {index + 1}
                          </div>
                          <div>
                            <p className="font-medium text-xs">{model.name}</p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">{model.provider}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-xs font-semibold">${model.cost.toFixed(2)}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      No model usage data yet
                    </p>
                  </div>
                )}
              </div>
            </div>
          </DashboardCardContent>
        </DashboardCard>
      </div>

      {/* AI Generation Workflow */}
      <div>
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold dashboard-text">AI Generation Workflow</h3>
          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
            <span>Progress:</span>
            <div className="w-32 h-2 bg-gray-200 dark:bg-gray-700 rounded-full">
              <div className="w-1/4 h-2 bg-blue-600 rounded-full"></div>
            </div>
            <span>25%</span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Step 1: Research */}
          <Link href="/dashboard/ai-generator/research">
            <DashboardCard className="relative hover:shadow-lg transition-all duration-300 cursor-pointer group hover:scale-105 border-2 border-blue-200 dark:border-blue-800">
              <DashboardCardHeader>
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center group-hover:bg-blue-600 transition-colors">
                    <span className="text-lg font-bold text-blue-600 dark:text-blue-400 group-hover:text-white">1</span>
                  </div>
                  <DashboardCardTitle className="text-lg">Research</DashboardCardTitle>
                </div>
                <DashboardCardDescription>
                  AI researches your topic and finds relevant keywords
                </DashboardCardDescription>
              </DashboardCardHeader>
              <DashboardCardContent>
                <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-4">
                  <MagnifyingGlassIcon className="w-4 h-4" />
                  <span>Keyword research & analysis</span>
                </div>
                <div className="flex items-center justify-between">
                  <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                    Ready
                  </Badge>
                  <span className="text-xs text-gray-500">~2 min</span>
                </div>
              </DashboardCardContent>
            </DashboardCard>
          </Link>

          {/* Step 2: Outline */}
          <DashboardCard className="relative hover:shadow-lg transition-all duration-300 opacity-60">
            <DashboardCardHeader>
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                  <span className="text-lg font-bold text-gray-400">2</span>
                </div>
                <DashboardCardTitle className="text-lg">Outline</DashboardCardTitle>
              </div>
              <DashboardCardDescription>
                AI creates a comprehensive blog structure
              </DashboardCardDescription>
            </DashboardCardHeader>
            <DashboardCardContent>
              <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-4">
                <DocumentTextIcon className="w-4 h-4" />
                <span>Structure & organization</span>
              </div>
              <div className="flex items-center justify-between">
                <Badge variant="outline">
                  Pending
                </Badge>
                <span className="text-xs text-gray-500">~3 min</span>
              </div>
            </DashboardCardContent>
          </DashboardCard>

          {/* Step 3: Generate */}
          <DashboardCard className="relative hover:shadow-lg transition-all duration-300 opacity-60">
            <DashboardCardHeader>
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                  <span className="text-lg font-bold text-gray-400">3</span>
                </div>
                <DashboardCardTitle className="text-lg">Generate</DashboardCardTitle>
              </div>
              <DashboardCardDescription>
                AI writes your complete blog post content
              </DashboardCardDescription>
            </DashboardCardHeader>
            <DashboardCardContent>
              <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-4">
                <PencilIcon className="w-4 h-4" />
                <span>Full content generation</span>
              </div>
              <div className="flex items-center justify-between">
                <Badge variant="outline">
                  Pending
                </Badge>
                <span className="text-xs text-gray-500">~5 min</span>
              </div>
            </DashboardCardContent>
          </DashboardCard>

          {/* Step 4: Publish */}
          <DashboardCard className="relative hover:shadow-lg transition-all duration-300 opacity-60">
            <DashboardCardHeader>
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                  <span className="text-lg font-bold text-gray-400">4</span>
                </div>
                <DashboardCardTitle className="text-lg">Publish</DashboardCardTitle>
              </div>
              <DashboardCardDescription>
                Review, edit and publish your blog post
              </DashboardCardDescription>
            </DashboardCardHeader>
            <DashboardCardContent>
              <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400 mb-4">
                <RocketLaunchIcon className="w-4 h-4" />
                <span>Final review & publishing</span>
              </div>
              <div className="flex items-center justify-between">
                <Badge variant="outline">
                  Pending
                </Badge>
                <span className="text-xs text-gray-500">~2 min</span>
              </div>
            </DashboardCardContent>
          </DashboardCard>
        </div>

        {/* Workflow Tips */}
        <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <div className="flex items-start gap-3">
            <InformationCircleIcon className="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="font-semibold text-blue-800 dark:text-blue-200">Workflow Tips</h4>
              <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                Follow the steps in order for best results. Each step builds on the previous one to create high-quality, SEO-optimized blog content.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
